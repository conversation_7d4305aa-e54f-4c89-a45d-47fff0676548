import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../theme/app_colors.dart';
import '../../theme/app_typography.dart';
import '../../theme/app_spacing.dart';

/// Luminar Input Component
/// 
/// A comprehensive input component that matches the shadcn/ui input patterns
/// with Material Design 3 styling and enhanced functionality.
enum LuminarInputVariant {
  outlined,
  filled,
  underlined,
}

enum LuminarInputSize {
  small,
  medium,
  large,
}

class LuminarInput extends StatefulWidget {
  const LuminarInput({
    super.key,
    this.controller,
    this.initialValue,
    this.focusNode,
    this.decoration,
    this.keyboardType,
    this.textInputAction,
    this.textCapitalization = TextCapitalization.none,
    this.style,
    this.textAlign = TextAlign.start,
    this.textAlignVertical,
    this.textDirection,
    this.readOnly = false,
    this.showCursor,
    this.autofocus = false,
    this.obscureText = false,
    this.autocorrect = true,
    this.smartDashesType,
    this.smartQuotesType,
    this.enableSuggestions = true,
    this.maxLines = 1,
    this.minLines,
    this.expands = false,
    this.maxLength,
    this.maxLengthEnforcement,
    this.onChanged,
    this.onEditingComplete,
    this.onSubmitted,
    this.onAppPrivateCommand,
    this.inputFormatters,
    this.enabled,
    this.cursorWidth = 2.0,
    this.cursorHeight,
    this.cursorRadius,
    this.cursorColor,
    this.selectionHeightStyle = BoxHeightStyle.tight,
    this.selectionWidthStyle = BoxWidthStyle.tight,
    this.keyboardAppearance,
    this.scrollPadding = const EdgeInsets.all(20.0),
    this.dragStartBehavior = DragStartBehavior.start,
    this.enableInteractiveSelection,
    this.selectionControls,
    this.onTap,
    this.mouseCursor,
    this.buildCounter,
    this.scrollController,
    this.scrollPhysics,
    this.autofillHints = const <String>[],
    this.clipBehavior = Clip.hardEdge,
    this.restorationId,
    this.scribbleEnabled = true,
    this.enableIMEPersonalizedLearning = true,
    // Custom properties
    this.variant = LuminarInputVariant.outlined,
    this.size = LuminarInputSize.medium,
    this.label,
    this.hint,
    this.helperText,
    this.errorText,
    this.prefixIcon,
    this.suffixIcon,
    this.prefix,
    this.suffix,
    this.isRequired = false,
    this.showRequiredIndicator = true,
  });

  // Standard TextFormField properties
  final TextEditingController? controller;
  final String? initialValue;
  final FocusNode? focusNode;
  final InputDecoration? decoration;
  final TextInputType? keyboardType;
  final TextInputAction? textInputAction;
  final TextCapitalization textCapitalization;
  final TextStyle? style;
  final TextAlign textAlign;
  final TextAlignVertical? textAlignVertical;
  final TextDirection? textDirection;
  final bool readOnly;
  final bool? showCursor;
  final bool autofocus;
  final bool obscureText;
  final bool autocorrect;
  final SmartDashesType? smartDashesType;
  final SmartQuotesType? smartQuotesType;
  final bool enableSuggestions;
  final int? maxLines;
  final int? minLines;
  final bool expands;
  final int? maxLength;
  final MaxLengthEnforcement? maxLengthEnforcement;
  final ValueChanged<String>? onChanged;
  final VoidCallback? onEditingComplete;
  final ValueChanged<String>? onSubmitted;
  final AppPrivateCommandCallback? onAppPrivateCommand;
  final List<TextInputFormatter>? inputFormatters;
  final bool? enabled;
  final double cursorWidth;
  final double? cursorHeight;
  final Radius? cursorRadius;
  final Color? cursorColor;
  final BoxHeightStyle selectionHeightStyle;
  final BoxWidthStyle selectionWidthStyle;
  final Brightness? keyboardAppearance;
  final EdgeInsets scrollPadding;
  final DragStartBehavior dragStartBehavior;
  final bool? enableInteractiveSelection;
  final TextSelectionControls? selectionControls;
  final GestureTapCallback? onTap;
  final MouseCursor? mouseCursor;
  final InputCounterWidgetBuilder? buildCounter;
  final ScrollController? scrollController;
  final ScrollPhysics? scrollPhysics;
  final Iterable<String>? autofillHints;
  final Clip clipBehavior;
  final String? restorationId;
  final bool scribbleEnabled;
  final bool enableIMEPersonalizedLearning;

  // Custom properties
  final LuminarInputVariant variant;
  final LuminarInputSize size;
  final String? label;
  final String? hint;
  final String? helperText;
  final String? errorText;
  final Widget? prefixIcon;
  final Widget? suffixIcon;
  final Widget? prefix;
  final Widget? suffix;
  final bool isRequired;
  final bool showRequiredIndicator;

  @override
  State<LuminarInput> createState() => _LuminarInputState();
}

class _LuminarInputState extends State<LuminarInput> {
  late FocusNode _focusNode;
  bool _isFocused = false;

  @override
  void initState() {
    super.initState();
    _focusNode = widget.focusNode ?? FocusNode();
    _focusNode.addListener(_onFocusChange);
  }

  @override
  void dispose() {
    if (widget.focusNode == null) {
      _focusNode.dispose();
    } else {
      _focusNode.removeListener(_onFocusChange);
    }
    super.dispose();
  }

  void _onFocusChange() {
    setState(() {
      _isFocused = _focusNode.hasFocus;
    });
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    
    final inputDecoration = _buildInputDecoration(colorScheme);
    final textStyle = _getTextStyle(theme);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: [
        if (widget.label != null) _buildLabel(theme),
        TextFormField(
          controller: widget.controller,
          initialValue: widget.initialValue,
          focusNode: _focusNode,
          decoration: inputDecoration,
          keyboardType: widget.keyboardType,
          textInputAction: widget.textInputAction,
          textCapitalization: widget.textCapitalization,
          style: textStyle,
          textAlign: widget.textAlign,
          textAlignVertical: widget.textAlignVertical,
          textDirection: widget.textDirection,
          readOnly: widget.readOnly,
          showCursor: widget.showCursor,
          autofocus: widget.autofocus,
          obscureText: widget.obscureText,
          autocorrect: widget.autocorrect,
          smartDashesType: widget.smartDashesType,
          smartQuotesType: widget.smartQuotesType,
          enableSuggestions: widget.enableSuggestions,
          maxLines: widget.maxLines,
          minLines: widget.minLines,
          expands: widget.expands,
          maxLength: widget.maxLength,
          maxLengthEnforcement: widget.maxLengthEnforcement,
          onChanged: widget.onChanged,
          onEditingComplete: widget.onEditingComplete,
          onFieldSubmitted: widget.onSubmitted,
          onAppPrivateCommand: widget.onAppPrivateCommand,
          inputFormatters: widget.inputFormatters,
          enabled: widget.enabled,
          cursorWidth: widget.cursorWidth,
          cursorHeight: widget.cursorHeight,
          cursorRadius: widget.cursorRadius,
          cursorColor: widget.cursorColor ?? colorScheme.primary,
          selectionHeightStyle: widget.selectionHeightStyle,
          selectionWidthStyle: widget.selectionWidthStyle,
          keyboardAppearance: widget.keyboardAppearance,
          scrollPadding: widget.scrollPadding,
          dragStartBehavior: widget.dragStartBehavior,
          enableInteractiveSelection: widget.enableInteractiveSelection,
          selectionControls: widget.selectionControls,
          onTap: widget.onTap,
          mouseCursor: widget.mouseCursor,
          buildCounter: widget.buildCounter,
          scrollController: widget.scrollController,
          scrollPhysics: widget.scrollPhysics,
          autofillHints: widget.autofillHints,
          clipBehavior: widget.clipBehavior,
          restorationId: widget.restorationId,
          scribbleEnabled: widget.scribbleEnabled,
          enableIMEPersonalizedLearning: widget.enableIMEPersonalizedLearning,
        ),
        if (widget.helperText != null || widget.errorText != null)
          _buildHelperText(theme),
      ],
    );
  }

  Widget _buildLabel(ThemeData theme) {
    return Padding(
      padding: EdgeInsets.only(bottom: AppSpacing.sm),
      child: RichText(
        text: TextSpan(
          style: AppTypography.labelMedium.copyWith(
            color: theme.colorScheme.onSurface,
          ),
          children: [
            TextSpan(text: widget.label),
            if (widget.isRequired && widget.showRequiredIndicator)
              TextSpan(
                text: ' *',
                style: TextStyle(color: theme.colorScheme.error),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildHelperText(ThemeData theme) {
    final text = widget.errorText ?? widget.helperText;
    final isError = widget.errorText != null;
    
    return Padding(
      padding: EdgeInsets.only(top: AppSpacing.sm),
      child: Text(
        text!,
        style: AppTypography.caption.copyWith(
          color: isError 
              ? theme.colorScheme.error 
              : theme.colorScheme.onSurfaceVariant,
        ),
      ),
    );
  }

  InputDecoration _buildInputDecoration(ColorScheme colorScheme) {
    final hasError = widget.errorText != null;
    final contentPadding = _getContentPadding();
    
    return InputDecoration(
      hintText: widget.hint,
      prefixIcon: widget.prefixIcon,
      suffixIcon: widget.suffixIcon,
      prefix: widget.prefix,
      suffix: widget.suffix,
      contentPadding: contentPadding,
      filled: widget.variant == LuminarInputVariant.filled,
      fillColor: widget.variant == LuminarInputVariant.filled 
          ? colorScheme.surfaceContainerHighest 
          : null,
      border: _getBorder(colorScheme, false, false),
      enabledBorder: _getBorder(colorScheme, false, false),
      focusedBorder: _getBorder(colorScheme, true, false),
      errorBorder: _getBorder(colorScheme, false, true),
      focusedErrorBorder: _getBorder(colorScheme, true, true),
      disabledBorder: _getBorder(colorScheme.onSurface.withOpacity(0.12), false, false),
      hintStyle: AppTypography.bodyMedium.copyWith(
        color: colorScheme.onSurfaceVariant,
      ),
      errorStyle: AppTypography.caption.copyWith(
        color: colorScheme.error,
      ),
    );
  }

  InputBorder _getBorder(ColorScheme colorScheme, bool isFocused, bool hasError) {
    final color = hasError 
        ? colorScheme.error
        : isFocused 
            ? colorScheme.primary
            : colorScheme.outline;
    
    final width = isFocused ? 2.0 : 1.0;
    
    switch (widget.variant) {
      case LuminarInputVariant.outlined:
        return OutlineInputBorder(
          borderRadius: BorderRadius.circular(AppSpacing.inputRadius),
          borderSide: BorderSide(color: color, width: width),
        );
      
      case LuminarInputVariant.filled:
        return OutlineInputBorder(
          borderRadius: BorderRadius.circular(AppSpacing.inputRadius),
          borderSide: isFocused || hasError 
              ? BorderSide(color: color, width: width)
              : BorderSide.none,
        );
      
      case LuminarInputVariant.underlined:
        return UnderlineInputBorder(
          borderSide: BorderSide(color: color, width: width),
        );
    }
  }

  InputBorder _getBorder(Color color, bool isFocused, bool hasError) {
    final width = isFocused ? 2.0 : 1.0;
    
    switch (widget.variant) {
      case LuminarInputVariant.outlined:
        return OutlineInputBorder(
          borderRadius: BorderRadius.circular(AppSpacing.inputRadius),
          borderSide: BorderSide(color: color, width: width),
        );
      
      case LuminarInputVariant.filled:
        return OutlineInputBorder(
          borderRadius: BorderRadius.circular(AppSpacing.inputRadius),
          borderSide: isFocused || hasError 
              ? BorderSide(color: color, width: width)
              : BorderSide.none,
        );
      
      case LuminarInputVariant.underlined:
        return UnderlineInputBorder(
          borderSide: BorderSide(color: color, width: width),
        );
    }
  }

  EdgeInsets _getContentPadding() {
    return switch (widget.size) {
      LuminarInputSize.small => AppSpacing.symmetric(
        horizontal: AppSpacing.md,
        vertical: AppSpacing.sm,
      ),
      LuminarInputSize.medium => AppSpacing.symmetric(
        horizontal: AppSpacing.lg,
        vertical: AppSpacing.md,
      ),
      LuminarInputSize.large => AppSpacing.symmetric(
        horizontal: AppSpacing.xl,
        vertical: AppSpacing.lg,
      ),
    };
  }

  TextStyle _getTextStyle(ThemeData theme) {
    final baseStyle = switch (widget.size) {
      LuminarInputSize.small => AppTypography.bodySmall,
      LuminarInputSize.medium => AppTypography.bodyMedium,
      LuminarInputSize.large => AppTypography.bodyLarge,
    };

    return widget.style ?? baseStyle.copyWith(
      color: theme.colorScheme.onSurface,
    );
  }
}
