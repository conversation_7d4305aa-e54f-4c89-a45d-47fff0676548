import 'package:flutter/material.dart';
import '../../theme/app_colors.dart';
import '../../theme/app_spacing.dart';
import '../../theme/app_theme_extensions.dart';

/// Luminar Card Component
/// 
/// A flexible card component that matches the shadcn/ui card patterns
/// with Material Design 3 styling and consistent elevation.
enum LuminarCardVariant {
  elevated,
  outlined,
  filled,
  ghost,
}

class LuminarCard extends StatelessWidget {
  const LuminarCard({
    super.key,
    required this.child,
    this.variant = LuminarCardVariant.elevated,
    this.padding,
    this.margin,
    this.width,
    this.height,
    this.onTap,
    this.onLongPress,
    this.borderRadius,
    this.clipBehavior = Clip.antiAlias,
    this.semanticContainer = true,
  });

  final Widget child;
  final LuminarCardVariant variant;
  final EdgeInsetsGeometry? padding;
  final EdgeInsetsGeometry? margin;
  final double? width;
  final double? height;
  final VoidCallback? onTap;
  final VoidCallback? onLongPress;
  final BorderRadius? borderRadius;
  final Clip clipBehavior;
  final bool semanticContainer;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    final shadows = theme.shadows;
    
    final cardStyle = _getCardStyle(colorScheme, shadows);
    final effectiveBorderRadius = borderRadius ?? 
        BorderRadius.circular(AppSpacing.radiusMedium);
    final effectivePadding = padding ?? 
        AppSpacing.all(AppSpacing.cardPadding);
    final effectiveMargin = margin ?? 
        AppSpacing.all(AppSpacing.cardMargin);

    Widget card = Container(
      width: width,
      height: height,
      margin: effectiveMargin,
      decoration: BoxDecoration(
        color: cardStyle.backgroundColor,
        border: cardStyle.border,
        borderRadius: effectiveBorderRadius,
        boxShadow: cardStyle.boxShadow,
      ),
      child: Material(
        color: Colors.transparent,
        child: _buildCardContent(effectivePadding, effectiveBorderRadius),
      ),
    );

    return card;
  }

  Widget _buildCardContent(EdgeInsetsGeometry padding, BorderRadius borderRadius) {
    Widget content = Padding(
      padding: padding,
      child: child,
    );

    // Add interaction if onTap or onLongPress is provided
    if (onTap != null || onLongPress != null) {
      content = InkWell(
        onTap: onTap,
        onLongPress: onLongPress,
        borderRadius: borderRadius,
        child: content,
      );
    }

    return ClipRRect(
      borderRadius: borderRadius,
      clipBehavior: clipBehavior,
      child: content,
    );
  }

  _CardStyle _getCardStyle(ColorScheme colorScheme, ShadowTheme shadows) {
    switch (variant) {
      case LuminarCardVariant.elevated:
        return _CardStyle(
          backgroundColor: colorScheme.surface,
          boxShadow: shadows.medium,
        );
      
      case LuminarCardVariant.outlined:
        return _CardStyle(
          backgroundColor: colorScheme.surface,
          border: Border.all(
            color: colorScheme.outline,
            width: 1,
          ),
        );
      
      case LuminarCardVariant.filled:
        return _CardStyle(
          backgroundColor: colorScheme.surfaceContainerHighest,
        );
      
      case LuminarCardVariant.ghost:
        return _CardStyle(
          backgroundColor: Colors.transparent,
        );
    }
  }
}

/// Card Header Component
class LuminarCardHeader extends StatelessWidget {
  const LuminarCardHeader({
    super.key,
    this.title,
    this.subtitle,
    this.leading,
    this.trailing,
    this.padding,
  });

  final Widget? title;
  final Widget? subtitle;
  final Widget? leading;
  final Widget? trailing;
  final EdgeInsetsGeometry? padding;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final effectivePadding = padding ?? 
        AppSpacing.symmetric(
          horizontal: AppSpacing.xl,
          vertical: AppSpacing.lg,
        );

    return Padding(
      padding: effectivePadding,
      child: Row(
        children: [
          if (leading != null) ...[
            leading!,
            SizedBox(width: AppSpacing.md),
          ],
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                if (title != null)
                  DefaultTextStyle(
                    style: theme.textTheme.titleMedium!,
                    child: title!,
                  ),
                if (subtitle != null) ...[
                  SizedBox(height: AppSpacing.xs),
                  DefaultTextStyle(
                    style: theme.textTheme.bodyMedium!.copyWith(
                      color: theme.colorScheme.onSurfaceVariant,
                    ),
                    child: subtitle!,
                  ),
                ],
              ],
            ),
          ),
          if (trailing != null) ...[
            SizedBox(width: AppSpacing.md),
            trailing!,
          ],
        ],
      ),
    );
  }
}

/// Card Content Component
class LuminarCardContent extends StatelessWidget {
  const LuminarCardContent({
    super.key,
    required this.child,
    this.padding,
  });

  final Widget child;
  final EdgeInsetsGeometry? padding;

  @override
  Widget build(BuildContext context) {
    final effectivePadding = padding ?? 
        AppSpacing.symmetric(
          horizontal: AppSpacing.xl,
          vertical: AppSpacing.md,
        );

    return Padding(
      padding: effectivePadding,
      child: child,
    );
  }
}

/// Card Footer Component
class LuminarCardFooter extends StatelessWidget {
  const LuminarCardFooter({
    super.key,
    required this.child,
    this.padding,
    this.alignment = MainAxisAlignment.end,
  });

  final Widget child;
  final EdgeInsetsGeometry? padding;
  final MainAxisAlignment alignment;

  @override
  Widget build(BuildContext context) {
    final effectivePadding = padding ?? 
        AppSpacing.symmetric(
          horizontal: AppSpacing.xl,
          vertical: AppSpacing.lg,
        );

    return Padding(
      padding: effectivePadding,
      child: Row(
        mainAxisAlignment: alignment,
        children: [child],
      ),
    );
  }
}

/// Complete Card with Header, Content, and Footer
class LuminarCardComplete extends StatelessWidget {
  const LuminarCardComplete({
    super.key,
    this.header,
    required this.content,
    this.footer,
    this.variant = LuminarCardVariant.elevated,
    this.margin,
    this.onTap,
    this.onLongPress,
  });

  final Widget? header;
  final Widget content;
  final Widget? footer;
  final LuminarCardVariant variant;
  final EdgeInsetsGeometry? margin;
  final VoidCallback? onTap;
  final VoidCallback? onLongPress;

  @override
  Widget build(BuildContext context) {
    return LuminarCard(
      variant: variant,
      margin: margin,
      padding: EdgeInsets.zero,
      onTap: onTap,
      onLongPress: onLongPress,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        mainAxisSize: MainAxisSize.min,
        children: [
          if (header != null) header!,
          if (header != null && content != null)
            Divider(
              height: 1,
              thickness: 1,
              color: Theme.of(context).colorScheme.outline.withOpacity(0.2),
            ),
          LuminarCardContent(child: content),
          if (footer != null) ...[
            Divider(
              height: 1,
              thickness: 1,
              color: Theme.of(context).colorScheme.outline.withOpacity(0.2),
            ),
            footer!,
          ],
        ],
      ),
    );
  }
}

class _CardStyle {
  const _CardStyle({
    required this.backgroundColor,
    this.border,
    this.boxShadow,
  });

  final Color backgroundColor;
  final Border? border;
  final List<BoxShadow>? boxShadow;
}
