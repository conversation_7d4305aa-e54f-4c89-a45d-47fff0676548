import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import '../../theme/app_colors.dart';
import '../../theme/app_typography.dart';
import '../../theme/app_spacing.dart';

/// Luminar Button Component
/// 
/// A comprehensive button component that matches the shadcn/ui button patterns
/// from the migration apps with Material Design 3 styling.
enum LuminarButtonVariant {
  primary,
  secondary,
  outline,
  ghost,
  destructive,
  link,
}

enum LuminarButtonSize {
  small,
  medium,
  large,
  icon,
}

class LuminarButton extends StatefulWidget {
  const LuminarButton({
    super.key,
    required this.onPressed,
    required this.child,
    this.variant = LuminarButtonVariant.primary,
    this.size = LuminarButtonSize.medium,
    this.isLoading = false,
    this.isDisabled = false,
    this.fullWidth = false,
    this.leftIcon,
    this.rightIcon,
    this.tooltip,
    this.rippleEffect = true,
    this.animateOnPress = true,
  });

  final VoidCallback? onPressed;
  final Widget child;
  final LuminarButtonVariant variant;
  final LuminarButtonSize size;
  final bool isLoading;
  final bool isDisabled;
  final bool fullWidth;
  final Widget? leftIcon;
  final Widget? rightIcon;
  final String? tooltip;
  final bool rippleEffect;
  final bool animateOnPress;

  @override
  State<LuminarButton> createState() => _LuminarButtonState();
}

class _LuminarButtonState extends State<LuminarButton>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  bool _isPressed = false;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: AppSpacing.durationFastMs,
      vsync: this,
    );
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  bool get _isInteractive => 
      widget.onPressed != null && !widget.isDisabled && !widget.isLoading;

  void _handleTapDown(TapDownDetails details) {
    if (!_isInteractive || !widget.animateOnPress) return;
    setState(() => _isPressed = true);
    _animationController.forward();
  }

  void _handleTapUp(TapUpDetails details) {
    if (!widget.animateOnPress) return;
    setState(() => _isPressed = false);
    _animationController.reverse();
  }

  void _handleTapCancel() {
    if (!widget.animateOnPress) return;
    setState(() => _isPressed = false);
    _animationController.reverse();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    
    final buttonStyle = _getButtonStyle(colorScheme);
    final textStyle = _getTextStyle();
    final padding = _getPadding();
    final height = _getHeight();

    Widget button = Container(
      width: widget.fullWidth ? double.infinity : null,
      height: height,
      child: Material(
        color: buttonStyle.backgroundColor,
        borderRadius: BorderRadius.circular(AppSpacing.radiusMedium),
        child: InkWell(
          onTap: _isInteractive ? widget.onPressed : null,
          onTapDown: _handleTapDown,
          onTapUp: _handleTapUp,
          onTapCancel: _handleTapCancel,
          borderRadius: BorderRadius.circular(AppSpacing.radiusMedium),
          splashColor: widget.rippleEffect ? buttonStyle.splashColor : Colors.transparent,
          highlightColor: widget.rippleEffect ? buttonStyle.highlightColor : Colors.transparent,
          child: Container(
            padding: padding,
            decoration: BoxDecoration(
              border: buttonStyle.border,
              borderRadius: BorderRadius.circular(AppSpacing.radiusMedium),
            ),
            child: _buildButtonContent(textStyle),
          ),
        ),
      ),
    );

    // Apply press animation
    if (widget.animateOnPress) {
      button = AnimatedBuilder(
        animation: _animationController,
        builder: (context, child) {
          final scale = 1.0 - (_animationController.value * 0.02);
          return Transform.scale(
            scale: scale,
            child: child,
          );
        },
        child: button,
      );
    }

    // Apply loading state animation
    if (widget.isLoading) {
      button = button.animate(onPlay: (controller) => controller.repeat())
          .shimmer(duration: 1500.ms, color: Colors.white.withOpacity(0.3));
    }

    // Wrap with tooltip if provided
    if (widget.tooltip != null) {
      button = Tooltip(
        message: widget.tooltip!,
        child: button,
      );
    }

    return button;
  }

  Widget _buildButtonContent(TextStyle textStyle) {
    final List<Widget> children = [];

    // Left icon
    if (widget.leftIcon != null && !widget.isLoading) {
      children.add(widget.leftIcon!);
      if (widget.size != LuminarButtonSize.icon) {
        children.add(SizedBox(width: AppSpacing.sm));
      }
    }

    // Loading indicator or main content
    if (widget.isLoading) {
      children.add(
        SizedBox(
          width: _getIconSize(),
          height: _getIconSize(),
          child: CircularProgressIndicator(
            strokeWidth: 2,
            valueColor: AlwaysStoppedAnimation<Color>(textStyle.color!),
          ),
        ),
      );
      if (widget.size != LuminarButtonSize.icon) {
        children.add(SizedBox(width: AppSpacing.sm));
        children.add(
          DefaultTextStyle(
            style: textStyle,
            child: widget.child,
          ),
        );
      }
    } else {
      if (widget.size != LuminarButtonSize.icon) {
        children.add(
          DefaultTextStyle(
            style: textStyle,
            child: widget.child,
          ),
        );
      } else {
        children.add(widget.child);
      }
    }

    // Right icon
    if (widget.rightIcon != null && !widget.isLoading) {
      if (widget.size != LuminarButtonSize.icon) {
        children.add(SizedBox(width: AppSpacing.sm));
      }
      children.add(widget.rightIcon!);
    }

    return Row(
      mainAxisSize: MainAxisSize.min,
      mainAxisAlignment: MainAxisAlignment.center,
      children: children,
    );
  }

  _ButtonStyle _getButtonStyle(ColorScheme colorScheme) {
    switch (widget.variant) {
      case LuminarButtonVariant.primary:
        return _ButtonStyle(
          backgroundColor: _isInteractive ? colorScheme.primary : colorScheme.onSurface.withOpacity(0.12),
          foregroundColor: _isInteractive ? colorScheme.onPrimary : colorScheme.onSurface.withOpacity(0.38),
          splashColor: colorScheme.onPrimary.withOpacity(0.12),
          highlightColor: colorScheme.onPrimary.withOpacity(0.08),
        );
      
      case LuminarButtonVariant.secondary:
        return _ButtonStyle(
          backgroundColor: _isInteractive ? colorScheme.secondary : colorScheme.onSurface.withOpacity(0.12),
          foregroundColor: _isInteractive ? colorScheme.onSecondary : colorScheme.onSurface.withOpacity(0.38),
          splashColor: colorScheme.onSecondary.withOpacity(0.12),
          highlightColor: colorScheme.onSecondary.withOpacity(0.08),
        );
      
      case LuminarButtonVariant.outline:
        return _ButtonStyle(
          backgroundColor: Colors.transparent,
          foregroundColor: _isInteractive ? colorScheme.primary : colorScheme.onSurface.withOpacity(0.38),
          border: Border.all(
            color: _isInteractive ? colorScheme.outline : colorScheme.onSurface.withOpacity(0.12),
          ),
          splashColor: colorScheme.primary.withOpacity(0.12),
          highlightColor: colorScheme.primary.withOpacity(0.08),
        );
      
      case LuminarButtonVariant.ghost:
        return _ButtonStyle(
          backgroundColor: Colors.transparent,
          foregroundColor: _isInteractive ? colorScheme.primary : colorScheme.onSurface.withOpacity(0.38),
          splashColor: colorScheme.primary.withOpacity(0.12),
          highlightColor: colorScheme.primary.withOpacity(0.08),
        );
      
      case LuminarButtonVariant.destructive:
        return _ButtonStyle(
          backgroundColor: _isInteractive ? colorScheme.error : colorScheme.onSurface.withOpacity(0.12),
          foregroundColor: _isInteractive ? colorScheme.onError : colorScheme.onSurface.withOpacity(0.38),
          splashColor: colorScheme.onError.withOpacity(0.12),
          highlightColor: colorScheme.onError.withOpacity(0.08),
        );
      
      case LuminarButtonVariant.link:
        return _ButtonStyle(
          backgroundColor: Colors.transparent,
          foregroundColor: _isInteractive ? colorScheme.primary : colorScheme.onSurface.withOpacity(0.38),
          splashColor: colorScheme.primary.withOpacity(0.12),
          highlightColor: colorScheme.primary.withOpacity(0.08),
        );
    }
  }

  TextStyle _getTextStyle() {
    final baseStyle = switch (widget.size) {
      LuminarButtonSize.small => AppTypography.buttonSmall,
      LuminarButtonSize.medium => AppTypography.buttonMedium,
      LuminarButtonSize.large => AppTypography.buttonLarge,
      LuminarButtonSize.icon => AppTypography.buttonMedium,
    };

    final buttonStyle = _getButtonStyle(Theme.of(context).colorScheme);
    
    return baseStyle.copyWith(
      color: buttonStyle.foregroundColor,
      decoration: widget.variant == LuminarButtonVariant.link 
          ? TextDecoration.underline 
          : null,
    );
  }

  EdgeInsets _getPadding() {
    return switch (widget.size) {
      LuminarButtonSize.small => AppSpacing.symmetric(
        horizontal: AppSpacing.lg,
        vertical: AppSpacing.sm,
      ),
      LuminarButtonSize.medium => AppSpacing.symmetric(
        horizontal: AppSpacing.xl,
        vertical: AppSpacing.md,
      ),
      LuminarButtonSize.large => AppSpacing.symmetric(
        horizontal: AppSpacing.xl2,
        vertical: AppSpacing.lg,
      ),
      LuminarButtonSize.icon => AppSpacing.all(AppSpacing.md),
    };
  }

  double _getHeight() {
    return switch (widget.size) {
      LuminarButtonSize.small => 36,
      LuminarButtonSize.medium => 40,
      LuminarButtonSize.large => 44,
      LuminarButtonSize.icon => 40,
    };
  }

  double _getIconSize() {
    return switch (widget.size) {
      LuminarButtonSize.small => 16,
      LuminarButtonSize.medium => 20,
      LuminarButtonSize.large => 24,
      LuminarButtonSize.icon => 20,
    };
  }
}

class _ButtonStyle {
  const _ButtonStyle({
    required this.backgroundColor,
    required this.foregroundColor,
    this.border,
    this.splashColor,
    this.highlightColor,
  });

  final Color backgroundColor;
  final Color foregroundColor;
  final Border? border;
  final Color? splashColor;
  final Color? highlightColor;
}
