import 'package:flutter/material.dart';
import 'app_colors.dart';
import 'app_spacing.dart';

/// Custom theme extensions for additional design system properties
/// that are not covered by Material Design's built-in theming.

/// Shadow theme extension for consistent elevation and shadows
@immutable
class ShadowTheme extends ThemeExtension<ShadowTheme> {
  const ShadowTheme({
    required this.small,
    required this.medium,
    required this.large,
    required this.extraLarge,
  });

  final List<BoxShadow> small;
  final List<BoxShadow> medium;
  final List<BoxShadow> large;
  final List<BoxShadow> extraLarge;

  @override
  ShadowTheme copyWith({
    List<BoxShadow>? small,
    List<BoxShadow>? medium,
    List<BoxShadow>? large,
    List<BoxShadow>? extraLarge,
  }) {
    return ShadowTheme(
      small: small ?? this.small,
      medium: medium ?? this.medium,
      large: large ?? this.large,
      extraLarge: extraLarge ?? this.extraLarge,
    );
  }

  @override
  ShadowTheme lerp(ThemeExtension<ShadowTheme>? other, double t) {
    if (other is! ShadowTheme) {
      return this;
    }
    return ShadowTheme(
      small: BoxShadow.lerpList(small, other.small, t) ?? small,
      medium: BoxShadow.lerpList(medium, other.medium, t) ?? medium,
      large: BoxShadow.lerpList(large, other.large, t) ?? large,
      extraLarge: BoxShadow.lerpList(extraLarge, other.extraLarge, t) ?? extraLarge,
    );
  }

  /// Light theme shadows
  static const ShadowTheme light = ShadowTheme(
    small: [
      BoxShadow(
        color: Color(0x0A000000),
        blurRadius: 2,
        offset: Offset(0, 1),
      ),
    ],
    medium: [
      BoxShadow(
        color: Color(0x0F000000),
        blurRadius: 4,
        offset: Offset(0, 2),
      ),
      BoxShadow(
        color: Color(0x0A000000),
        blurRadius: 2,
        offset: Offset(0, 1),
      ),
    ],
    large: [
      BoxShadow(
        color: Color(0x14000000),
        blurRadius: 8,
        offset: Offset(0, 4),
      ),
      BoxShadow(
        color: Color(0x0A000000),
        blurRadius: 4,
        offset: Offset(0, 2),
      ),
    ],
    extraLarge: [
      BoxShadow(
        color: Color(0x19000000),
        blurRadius: 16,
        offset: Offset(0, 8),
      ),
      BoxShadow(
        color: Color(0x0F000000),
        blurRadius: 8,
        offset: Offset(0, 4),
      ),
    ],
  );

  /// Dark theme shadows
  static const ShadowTheme dark = ShadowTheme(
    small: [
      BoxShadow(
        color: Color(0x1A000000),
        blurRadius: 2,
        offset: Offset(0, 1),
      ),
    ],
    medium: [
      BoxShadow(
        color: Color(0x26000000),
        blurRadius: 4,
        offset: Offset(0, 2),
      ),
      BoxShadow(
        color: Color(0x1A000000),
        blurRadius: 2,
        offset: Offset(0, 1),
      ),
    ],
    large: [
      BoxShadow(
        color: Color(0x33000000),
        blurRadius: 8,
        offset: Offset(0, 4),
      ),
      BoxShadow(
        color: Color(0x1A000000),
        blurRadius: 4,
        offset: Offset(0, 2),
      ),
    ],
    extraLarge: [
      BoxShadow(
        color: Color(0x40000000),
        blurRadius: 16,
        offset: Offset(0, 8),
      ),
      BoxShadow(
        color: Color(0x26000000),
        blurRadius: 8,
        offset: Offset(0, 4),
      ),
    ],
  );
}

/// Animation theme extension for consistent motion design
@immutable
class AnimationTheme extends ThemeExtension<AnimationTheme> {
  const AnimationTheme({
    required this.fast,
    required this.normal,
    required this.slow,
    required this.extraSlow,
    required this.curveDefault,
    required this.curveEaseIn,
    required this.curveEaseOut,
    required this.curveBounce,
  });

  final Duration fast;
  final Duration normal;
  final Duration slow;
  final Duration extraSlow;
  final Curve curveDefault;
  final Curve curveEaseIn;
  final Curve curveEaseOut;
  final Curve curveBounce;

  @override
  AnimationTheme copyWith({
    Duration? fast,
    Duration? normal,
    Duration? slow,
    Duration? extraSlow,
    Curve? curveDefault,
    Curve? curveEaseIn,
    Curve? curveEaseOut,
    Curve? curveBounce,
  }) {
    return AnimationTheme(
      fast: fast ?? this.fast,
      normal: normal ?? this.normal,
      slow: slow ?? this.slow,
      extraSlow: extraSlow ?? this.extraSlow,
      curveDefault: curveDefault ?? this.curveDefault,
      curveEaseIn: curveEaseIn ?? this.curveEaseIn,
      curveEaseOut: curveEaseOut ?? this.curveEaseOut,
      curveBounce: curveBounce ?? this.curveBounce,
    );
  }

  @override
  AnimationTheme lerp(ThemeExtension<AnimationTheme>? other, double t) {
    if (other is! AnimationTheme) {
      return this;
    }
    return AnimationTheme(
      fast: Duration(
        milliseconds: (fast.inMilliseconds + 
          (other.fast.inMilliseconds - fast.inMilliseconds) * t).round(),
      ),
      normal: Duration(
        milliseconds: (normal.inMilliseconds + 
          (other.normal.inMilliseconds - normal.inMilliseconds) * t).round(),
      ),
      slow: Duration(
        milliseconds: (slow.inMilliseconds + 
          (other.slow.inMilliseconds - slow.inMilliseconds) * t).round(),
      ),
      extraSlow: Duration(
        milliseconds: (extraSlow.inMilliseconds + 
          (other.extraSlow.inMilliseconds - extraSlow.inMilliseconds) * t).round(),
      ),
      curveDefault: other.curveDefault,
      curveEaseIn: other.curveEaseIn,
      curveEaseOut: other.curveEaseOut,
      curveBounce: other.curveBounce,
    );
  }

  static const AnimationTheme standard = AnimationTheme(
    fast: Duration(milliseconds: 150),
    normal: Duration(milliseconds: 250),
    slow: Duration(milliseconds: 350),
    extraSlow: Duration(milliseconds: 500),
    curveDefault: Curves.easeInOut,
    curveEaseIn: Curves.easeIn,
    curveEaseOut: Curves.easeOut,
    curveBounce: Curves.bounceOut,
  );
}

/// Spacing theme extension for responsive spacing
@immutable
class SpacingTheme extends ThemeExtension<SpacingTheme> {
  const SpacingTheme({
    required this.screenPadding,
    required this.sectionSpacing,
    required this.componentSpacing,
    required this.elementSpacing,
  });

  final double screenPadding;
  final double sectionSpacing;
  final double componentSpacing;
  final double elementSpacing;

  @override
  SpacingTheme copyWith({
    double? screenPadding,
    double? sectionSpacing,
    double? componentSpacing,
    double? elementSpacing,
  }) {
    return SpacingTheme(
      screenPadding: screenPadding ?? this.screenPadding,
      sectionSpacing: sectionSpacing ?? this.sectionSpacing,
      componentSpacing: componentSpacing ?? this.componentSpacing,
      elementSpacing: elementSpacing ?? this.elementSpacing,
    );
  }

  @override
  SpacingTheme lerp(ThemeExtension<SpacingTheme>? other, double t) {
    if (other is! SpacingTheme) {
      return this;
    }
    return SpacingTheme(
      screenPadding: screenPadding + (other.screenPadding - screenPadding) * t,
      sectionSpacing: sectionSpacing + (other.sectionSpacing - sectionSpacing) * t,
      componentSpacing: componentSpacing + (other.componentSpacing - componentSpacing) * t,
      elementSpacing: elementSpacing + (other.elementSpacing - elementSpacing) * t,
    );
  }

  static const SpacingTheme mobile = SpacingTheme(
    screenPadding: AppSpacing.xl,
    sectionSpacing: AppSpacing.xl3,
    componentSpacing: AppSpacing.lg,
    elementSpacing: AppSpacing.md,
  );

  static const SpacingTheme tablet = SpacingTheme(
    screenPadding: AppSpacing.xl2,
    sectionSpacing: AppSpacing.xl4,
    componentSpacing: AppSpacing.xl,
    elementSpacing: AppSpacing.lg,
  );

  static const SpacingTheme desktop = SpacingTheme(
    screenPadding: AppSpacing.xl3,
    sectionSpacing: AppSpacing.xl5,
    componentSpacing: AppSpacing.xl2,
    elementSpacing: AppSpacing.xl,
  );
}

/// Extension methods for easy access to theme extensions
extension ThemeExtensions on ThemeData {
  ShadowTheme get shadows => extension<ShadowTheme>() ?? ShadowTheme.light;
  AnimationTheme get animations => extension<AnimationTheme>() ?? AnimationTheme.standard;
  SpacingTheme get spacing => extension<SpacingTheme>() ?? SpacingTheme.mobile;
}
