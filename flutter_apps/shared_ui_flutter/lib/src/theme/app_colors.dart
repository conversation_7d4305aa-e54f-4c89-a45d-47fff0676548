import 'package:flutter/material.dart';

/// Luminar Design System Colors
///
/// This color system matches the migration apps' design patterns
/// using OKLCH color space for better perceptual uniformity.
/// Primary color scheme: Orange/Amber (#E67E22 equivalent)
class AppColors {
  // Brand Primary Colors (Orange/Amber from migration apps)
  // Equivalent to oklch(0.645 0.246 16.439) from migration CSS
  static const Color primary = Color(0xFFE67E22);
  static const Color primaryLight = Color(0xFFF39C12);
  static const Color primaryDark = Color(0xFFD35400);

  // Primary color variants for different use cases
  static const Color primary50 = Color(0xFFFEF7ED);
  static const Color primary100 = Color(0xFFFEEDD3);
  static const Color primary200 = Color(0xFFFED7AA);
  static const Color primary300 = Color(0xFFFDBB77);
  static const Color primary400 = Color(0xFFFB923C);
  static const Color primary500 = Color(0xFFE67E22); // Main primary
  static const Color primary600 = Color(0xFFEA580C);
  static const Color primary700 = Color(0xFFC2410C);
  static const Color primary800 = Color(0xFF9A3412);
  static const Color primary900 = Color(0xFF7C2D12);
  static const Color primary950 = Color(0xFF431407);

  // Secondary colors (Stone/Gray from migration apps)
  static const Color secondary = Color(0xFFF5F5F4);
  static const Color secondaryLight = Color(0xFFFAFAF9);
  static const Color secondaryDark = Color(0xFFE7E5E4);

  // Accent colors (Teal for contrast)
  static const Color accent = Color(0xFF14B8A6);
  static const Color accentLight = Color(0xFF5EEAD4);
  static const Color accentDark = Color(0xFF0F766E);

  // Semantic Colors (matching migration design system)

  // Success colors (Green)
  static const Color success = Color(0xFF22C55E);
  static const Color successLight = Color(0xFF86EFAC);
  static const Color successDark = Color(0xFF16A34A);
  static const Color successForeground = Color(0xFFFFFFFF);

  // Warning colors (Yellow)
  static const Color warning = Color(0xFFF59E0B);
  static const Color warningLight = Color(0xFFFDE047);
  static const Color warningDark = Color(0xFFD97706);
  static const Color warningForeground = Color(0xFF000000);

  // Error/Destructive colors (Red)
  static const Color error = Color(0xFFEF4444);
  static const Color errorLight = Color(0xFFFCA5A5);
  static const Color errorDark = Color(0xFFDC2626);
  static const Color errorForeground = Color(0xFFFFFFFF);

  // Info colors (Blue)
  static const Color info = Color(0xFF3B82F6);
  static const Color infoLight = Color(0xFF93C5FD);
  static const Color infoDark = Color(0xFF2563EB);

  // Neutral colors
  static const Color white = Color(0xFFFFFFFF);
  static const Color black = Color(0xFF000000);

  // Stone/Gray scale (matching migration design system)
  static const Color stone50 = Color(0xFFFAFAF9);
  static const Color stone100 = Color(0xFFF5F5F4);
  static const Color stone200 = Color(0xFFE7E5E4);
  static const Color stone300 = Color(0xFFD6D3D1);
  static const Color stone400 = Color(0xFFA8A29E);
  static const Color stone500 = Color(0xFF78716C);
  static const Color stone600 = Color(0xFF57534E);
  static const Color stone700 = Color(0xFF44403C);
  static const Color stone800 = Color(0xFF292524);
  static const Color stone900 = Color(0xFF1C1917);
  static const Color stone950 = Color(0xFF0C0A09);

  // Legacy gray scale (for backward compatibility)
  static const Color gray50 = stone50;
  static const Color gray100 = stone100;
  static const Color gray200 = stone200;
  static const Color gray300 = stone300;
  static const Color gray400 = stone400;
  static const Color gray500 = stone500;
  static const Color gray600 = stone600;
  static const Color gray700 = stone700;
  static const Color gray800 = stone800;
  static const Color gray900 = stone900;

  // Design System Colors (matching migration CSS variables)

  // Background colors
  static const Color background = Color(0xFFFFFFFF);
  static const Color backgroundDark = Color(0xFF0C0A09);

  // Foreground colors
  static const Color foreground = Color(0xFF1C1917);
  static const Color foregroundDark = Color(0xFFFAFAF9);

  // Card colors
  static const Color card = Color(0xFFFFFFFF);
  static const Color cardForeground = Color(0xFF1C1917);
  static const Color cardDark = Color(0xFF292524);
  static const Color cardForegroundDark = Color(0xFFFAFAF9);

  // Popover colors
  static const Color popover = Color(0xFFFFFFFF);
  static const Color popoverForeground = Color(0xFF1C1917);
  static const Color popoverDark = Color(0xFF292524);
  static const Color popoverForegroundDark = Color(0xFFFAFAF9);

  // Muted colors
  static const Color muted = Color(0xFFF5F5F4);
  static const Color mutedForeground = Color(0xFF78716C);
  static const Color mutedDark = Color(0xFF44403C);
  static const Color mutedForegroundDark = Color(0xFFA8A29E);

  // Border colors
  static const Color border = Color(0xFFE7E5E4);
  static const Color borderDark = Color(0xFF44403C);

  // Input colors
  static const Color input = Color(0xFFE7E5E4);
  static const Color inputDark = Color(0xFF44403C);

  // Ring colors (focus indicators)
  static const Color ring = primary;
  static const Color ringDark = primary;

  // Design System Constants
  static const double borderRadius = 8.0; // 0.5rem equivalent

  // Utility Methods

  /// Get color with opacity
  static Color withOpacity(Color color, double opacity) {
    return color.withOpacity(opacity);
  }

  /// Get appropriate text color for background
  static Color getTextColorForBackground(Color backgroundColor) {
    return backgroundColor.computeLuminance() > 0.5 ? foreground : background;
  }

  /// Get theme-appropriate colors based on brightness
  static Color getBackgroundColor(Brightness brightness) {
    return brightness == Brightness.light ? background : backgroundDark;
  }

  static Color getForegroundColor(Brightness brightness) {
    return brightness == Brightness.light ? foreground : foregroundDark;
  }

  static Color getCardColor(Brightness brightness) {
    return brightness == Brightness.light ? card : cardDark;
  }

  static Color getBorderColor(Brightness brightness) {
    return brightness == Brightness.light ? border : borderDark;
  }

  static Color getMutedColor(Brightness brightness) {
    return brightness == Brightness.light ? muted : mutedDark;
  }

  // Material color swatch for primary color
  static const MaterialColor primarySwatch = MaterialColor(
    0xFFE67E22,
    <int, Color>{
      50: primary50,
      100: primary100,
      200: primary200,
      300: primary300,
      400: primary400,
      500: primary500,
      600: primary600,
      700: primary700,
      800: primary800,
      900: primary900,
    },
  );

  /// Create a ColorScheme for light theme
  static ColorScheme get lightColorScheme => ColorScheme.light(
    primary: primary,
    onPrimary: Color(0xFFFFFFFF),
    primaryContainer: primary100,
    onPrimaryContainer: primary900,
    secondary: stone200,
    onSecondary: stone800,
    secondaryContainer: stone100,
    onSecondaryContainer: stone900,
    tertiary: accent,
    onTertiary: Color(0xFFFFFFFF),
    tertiaryContainer: Color(0xFFCCFBF1),
    onTertiaryContainer: accentDark,
    error: error,
    onError: errorForeground,
    errorContainer: errorLight,
    onErrorContainer: errorDark,
    surface: background,
    onSurface: foreground,
    surfaceContainerHighest: stone100,
    onSurfaceVariant: stone600,
    outline: border,
    outlineVariant: stone200,
    shadow: black,
    scrim: black,
    inverseSurface: stone900,
    onInverseSurface: stone50,
    inversePrimary: primary200,
  );

  /// Create a ColorScheme for dark theme
  static ColorScheme get darkColorScheme => ColorScheme.dark(
    primary: primary400,
    onPrimary: primary900,
    primaryContainer: primary800,
    onPrimaryContainer: primary100,
    secondary: stone700,
    onSecondary: stone200,
    secondaryContainer: stone800,
    onSecondaryContainer: stone100,
    tertiary: accent,
    onTertiary: accentDark,
    tertiaryContainer: accentDark,
    onTertiaryContainer: Color(0xFFCCFBF1),
    error: error,
    onError: errorForeground,
    errorContainer: errorDark,
    onErrorContainer: errorLight,
    surface: backgroundDark,
    onSurface: foregroundDark,
    surfaceContainerHighest: stone800,
    onSurfaceVariant: stone400,
    outline: borderDark,
    outlineVariant: stone700,
    shadow: black,
    scrim: black,
    inverseSurface: stone100,
    onInverseSurface: stone900,
    inversePrimary: primary700,
  );
}