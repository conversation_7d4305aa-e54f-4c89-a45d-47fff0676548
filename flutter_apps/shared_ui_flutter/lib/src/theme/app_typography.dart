import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

/// Luminar Design System Typography
/// 
/// This typography system matches the migration apps' Inter font family
/// and provides consistent text styles across all Flutter applications.
class AppTypography {
  // Font family
  static const String fontFamily = 'Inter';
  
  // Font weights
  static const FontWeight light = FontWeight.w300;
  static const FontWeight regular = FontWeight.w400;
  static const FontWeight medium = FontWeight.w500;
  static const FontWeight semiBold = FontWeight.w600;
  static const FontWeight bold = FontWeight.w700;
  static const FontWeight extraBold = FontWeight.w800;
  
  // Line heights (matching CSS line-height values)
  static const double lineHeightTight = 1.25;
  static const double lineHeightNormal = 1.5;
  static const double lineHeightRelaxed = 1.625;
  static const double lineHeightLoose = 2.0;
  
  // Letter spacing
  static const double letterSpacingTight = -0.025;
  static const double letterSpacingNormal = 0.0;
  static const double letterSpacingWide = 0.025;
  
  // Base text style with Inter font
  static TextStyle get baseTextStyle => GoogleFonts.inter(
    fontWeight: regular,
    letterSpacing: letterSpacingNormal,
  );
  
  // Display styles (for large headings)
  static TextStyle get displayLarge => baseTextStyle.copyWith(
    fontSize: 57,
    fontWeight: regular,
    height: lineHeightTight,
    letterSpacing: letterSpacingTight,
  );
  
  static TextStyle get displayMedium => baseTextStyle.copyWith(
    fontSize: 45,
    fontWeight: regular,
    height: lineHeightTight,
    letterSpacing: letterSpacingTight,
  );
  
  static TextStyle get displaySmall => baseTextStyle.copyWith(
    fontSize: 36,
    fontWeight: regular,
    height: lineHeightTight,
    letterSpacing: letterSpacingTight,
  );
  
  // Headline styles
  static TextStyle get headlineLarge => baseTextStyle.copyWith(
    fontSize: 32,
    fontWeight: semiBold,
    height: lineHeightTight,
    letterSpacing: letterSpacingTight,
  );
  
  static TextStyle get headlineMedium => baseTextStyle.copyWith(
    fontSize: 28,
    fontWeight: semiBold,
    height: lineHeightTight,
    letterSpacing: letterSpacingTight,
  );
  
  static TextStyle get headlineSmall => baseTextStyle.copyWith(
    fontSize: 24,
    fontWeight: semiBold,
    height: lineHeightTight,
    letterSpacing: letterSpacingTight,
  );
  
  // Title styles
  static TextStyle get titleLarge => baseTextStyle.copyWith(
    fontSize: 22,
    fontWeight: semiBold,
    height: lineHeightNormal,
    letterSpacing: letterSpacingNormal,
  );
  
  static TextStyle get titleMedium => baseTextStyle.copyWith(
    fontSize: 16,
    fontWeight: medium,
    height: lineHeightNormal,
    letterSpacing: letterSpacingWide,
  );
  
  static TextStyle get titleSmall => baseTextStyle.copyWith(
    fontSize: 14,
    fontWeight: medium,
    height: lineHeightNormal,
    letterSpacing: letterSpacingWide,
  );
  
  // Label styles
  static TextStyle get labelLarge => baseTextStyle.copyWith(
    fontSize: 14,
    fontWeight: medium,
    height: lineHeightNormal,
    letterSpacing: letterSpacingWide,
  );
  
  static TextStyle get labelMedium => baseTextStyle.copyWith(
    fontSize: 12,
    fontWeight: medium,
    height: lineHeightNormal,
    letterSpacing: letterSpacingWide,
  );
  
  static TextStyle get labelSmall => baseTextStyle.copyWith(
    fontSize: 11,
    fontWeight: medium,
    height: lineHeightNormal,
    letterSpacing: letterSpacingWide,
  );
  
  // Body styles
  static TextStyle get bodyLarge => baseTextStyle.copyWith(
    fontSize: 16,
    fontWeight: regular,
    height: lineHeightNormal,
    letterSpacing: letterSpacingNormal,
  );
  
  static TextStyle get bodyMedium => baseTextStyle.copyWith(
    fontSize: 14,
    fontWeight: regular,
    height: lineHeightNormal,
    letterSpacing: letterSpacingNormal,
  );
  
  static TextStyle get bodySmall => baseTextStyle.copyWith(
    fontSize: 12,
    fontWeight: regular,
    height: lineHeightNormal,
    letterSpacing: letterSpacingNormal,
  );
  
  // Custom styles for specific use cases
  
  // Button text
  static TextStyle get buttonLarge => baseTextStyle.copyWith(
    fontSize: 16,
    fontWeight: medium,
    height: 1.0,
    letterSpacing: letterSpacingWide,
  );
  
  static TextStyle get buttonMedium => baseTextStyle.copyWith(
    fontSize: 14,
    fontWeight: medium,
    height: 1.0,
    letterSpacing: letterSpacingWide,
  );
  
  static TextStyle get buttonSmall => baseTextStyle.copyWith(
    fontSize: 12,
    fontWeight: medium,
    height: 1.0,
    letterSpacing: letterSpacingWide,
  );
  
  // Caption and overline
  static TextStyle get caption => baseTextStyle.copyWith(
    fontSize: 12,
    fontWeight: regular,
    height: lineHeightNormal,
    letterSpacing: letterSpacingWide,
  );
  
  static TextStyle get overline => baseTextStyle.copyWith(
    fontSize: 10,
    fontWeight: medium,
    height: lineHeightNormal,
    letterSpacing: letterSpacingWide,
  );
  
  // Code and monospace
  static TextStyle get code => GoogleFonts.jetBrainsMono(
    fontSize: 14,
    fontWeight: regular,
    height: lineHeightNormal,
    letterSpacing: letterSpacingNormal,
  );
  
  static TextStyle get codeSmall => GoogleFonts.jetBrainsMono(
    fontSize: 12,
    fontWeight: regular,
    height: lineHeightNormal,
    letterSpacing: letterSpacingNormal,
  );
  
  /// Create a complete TextTheme for Material Design
  static TextTheme get textTheme => TextTheme(
    displayLarge: displayLarge,
    displayMedium: displayMedium,
    displaySmall: displaySmall,
    headlineLarge: headlineLarge,
    headlineMedium: headlineMedium,
    headlineSmall: headlineSmall,
    titleLarge: titleLarge,
    titleMedium: titleMedium,
    titleSmall: titleSmall,
    labelLarge: labelLarge,
    labelMedium: labelMedium,
    labelSmall: labelSmall,
    bodyLarge: bodyLarge,
    bodyMedium: bodyMedium,
    bodySmall: bodySmall,
  );
  
  /// Apply color to text style
  static TextStyle withColor(TextStyle style, Color color) {
    return style.copyWith(color: color);
  }
  
  /// Apply opacity to text style
  static TextStyle withOpacity(TextStyle style, double opacity) {
    return style.copyWith(color: style.color?.withOpacity(opacity));
  }
  
  /// Create responsive text style based on screen size
  static TextStyle responsive(
    TextStyle baseStyle, {
    double? mobileFactor,
    double? tabletFactor,
    double? desktopFactor,
  }) {
    // This would be used with MediaQuery to adjust font sizes
    // Implementation depends on the responsive framework used
    return baseStyle;
  }
}
