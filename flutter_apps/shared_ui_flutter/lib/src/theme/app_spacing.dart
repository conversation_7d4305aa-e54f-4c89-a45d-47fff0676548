import 'package:flutter/material.dart';

/// Luminar Design System Spacing
/// 
/// This spacing system provides consistent spacing values across all Flutter applications.
/// Based on a 4px base unit with Tailwind CSS-inspired naming.
class AppSpacing {
  // Base unit (4px)
  static const double baseUnit = 4.0;
  
  // Spacing scale (in logical pixels)
  static const double none = 0.0;
  static const double px = 1.0;
  static const double xs = baseUnit * 0.5; // 2px
  static const double sm = baseUnit * 1; // 4px
  static const double md = baseUnit * 2; // 8px
  static const double lg = baseUnit * 3; // 12px
  static const double xl = baseUnit * 4; // 16px
  static const double xl2 = baseUnit * 5; // 20px
  static const double xl3 = baseUnit * 6; // 24px
  static const double xl4 = baseUnit * 8; // 32px
  static const double xl5 = baseUnit * 10; // 40px
  static const double xl6 = baseUnit * 12; // 48px
  static const double xl7 = baseUnit * 14; // 56px
  static const double xl8 = baseUnit * 16; // 64px
  static const double xl9 = baseUnit * 18; // 72px
  static const double xl10 = baseUnit * 20; // 80px
  
  // Semantic spacing values
  static const double tiny = xs;
  static const double small = sm;
  static const double medium = md;
  static const double large = lg;
  static const double extraLarge = xl;
  
  // Component-specific spacing
  static const double buttonPaddingHorizontal = xl;
  static const double buttonPaddingVertical = md;
  static const double buttonPaddingSmall = lg;
  static const double buttonPaddingLarge = xl2;
  
  static const double cardPadding = xl;
  static const double cardMargin = md;
  static const double cardRadius = md;
  
  static const double inputPaddingHorizontal = lg;
  static const double inputPaddingVertical = md;
  static const double inputRadius = md;
  
  static const double dialogPadding = xl3;
  static const double dialogMargin = xl;
  
  static const double listItemPadding = xl;
  static const double listItemSpacing = sm;
  
  static const double sectionSpacing = xl4;
  static const double componentSpacing = xl;
  static const double elementSpacing = md;
  
  // Layout spacing
  static const double screenPadding = xl;
  static const double screenPaddingLarge = xl3;
  static const double contentMaxWidth = 1200.0;
  
  // Grid spacing
  static const double gridGutter = xl;
  static const double gridGutterSmall = md;
  static const double gridGutterLarge = xl2;
  
  // Border radius values
  static const double radiusNone = 0.0;
  static const double radiusSmall = sm;
  static const double radiusMedium = md;
  static const double radiusLarge = lg;
  static const double radiusExtraLarge = xl;
  static const double radiusRound = 9999.0; // Fully rounded
  
  // Shadow and elevation spacing
  static const double elevationNone = 0.0;
  static const double elevationSmall = 1.0;
  static const double elevationMedium = 4.0;
  static const double elevationLarge = 8.0;
  static const double elevationExtraLarge = 16.0;
  
  // Animation durations (in milliseconds)
  static const int durationFast = 150;
  static const int durationNormal = 250;
  static const int durationSlow = 350;
  static const int durationExtraSlow = 500;
  
  // Breakpoints for responsive design
  static const double breakpointMobile = 640.0;
  static const double breakpointTablet = 768.0;
  static const double breakpointDesktop = 1024.0;
  static const double breakpointLargeDesktop = 1280.0;
  static const double breakpointExtraLarge = 1536.0;
  
  // Utility methods
  
  /// Get spacing value by multiplier
  static double space(double multiplier) => baseUnit * multiplier;
  
  /// Get horizontal padding
  static EdgeInsets horizontal(double value) => EdgeInsets.symmetric(horizontal: value);
  
  /// Get vertical padding
  static EdgeInsets vertical(double value) => EdgeInsets.symmetric(vertical: value);
  
  /// Get symmetric padding
  static EdgeInsets symmetric({double horizontal = 0, double vertical = 0}) =>
      EdgeInsets.symmetric(horizontal: horizontal, vertical: vertical);
  
  /// Get all-around padding
  static EdgeInsets all(double value) => EdgeInsets.all(value);
  
  /// Get custom padding
  static EdgeInsets only({
    double left = 0,
    double top = 0,
    double right = 0,
    double bottom = 0,
  }) =>
      EdgeInsets.only(left: left, top: top, right: right, bottom: bottom);
  
  /// Get padding from LTRB values
  static EdgeInsets fromLTRB(double left, double top, double right, double bottom) =>
      EdgeInsets.fromLTRB(left, top, right, bottom);
  
  /// Get margin (alias for padding methods)
  static EdgeInsets marginHorizontal(double value) => horizontal(value);
  static EdgeInsets marginVertical(double value) => vertical(value);
  static EdgeInsets marginSymmetric({double horizontal = 0, double vertical = 0}) =>
      symmetric(horizontal: horizontal, vertical: vertical);
  static EdgeInsets marginAll(double value) => all(value);
  
  /// Get SizedBox for spacing
  static Widget height(double value) => SizedBox(height: value);
  static Widget width(double value) => SizedBox(width: value);
  static Widget square(double value) => SizedBox(width: value, height: value);
  
  /// Get responsive spacing based on screen width
  static double responsive(
    BuildContext context, {
    double mobile = md,
    double tablet = lg,
    double desktop = xl,
  }) {
    final width = MediaQuery.of(context).size.width;
    if (width < breakpointTablet) return mobile;
    if (width < breakpointDesktop) return tablet;
    return desktop;
  }
  
  /// Get responsive padding
  static EdgeInsets responsivePadding(
    BuildContext context, {
    EdgeInsets mobile = const EdgeInsets.all(md),
    EdgeInsets tablet = const EdgeInsets.all(lg),
    EdgeInsets desktop = const EdgeInsets.all(xl),
  }) {
    final width = MediaQuery.of(context).size.width;
    if (width < breakpointTablet) return mobile;
    if (width < breakpointDesktop) return tablet;
    return desktop;
  }
  
  /// Get border radius
  static BorderRadius radius(double value) => BorderRadius.circular(value);
  static BorderRadius radiusTop(double value) => BorderRadius.vertical(top: Radius.circular(value));
  static BorderRadius radiusBottom(double value) => BorderRadius.vertical(bottom: Radius.circular(value));
  static BorderRadius radiusLeft(double value) => BorderRadius.horizontal(left: Radius.circular(value));
  static BorderRadius radiusRight(double value) => BorderRadius.horizontal(right: Radius.circular(value));
  
  /// Get custom border radius
  static BorderRadius radiusOnly({
    double topLeft = 0,
    double topRight = 0,
    double bottomLeft = 0,
    double bottomRight = 0,
  }) =>
      BorderRadius.only(
        topLeft: Radius.circular(topLeft),
        topRight: Radius.circular(topRight),
        bottomLeft: Radius.circular(bottomLeft),
        bottomRight: Radius.circular(bottomRight),
      );
  
  /// Common spacing widgets
  static const Widget spacerTiny = SizedBox(height: tiny);
  static const Widget spacerSmall = SizedBox(height: small);
  static const Widget spacerMedium = SizedBox(height: medium);
  static const Widget spacerLarge = SizedBox(height: large);
  static const Widget spacerExtraLarge = SizedBox(height: extraLarge);
  
  static const Widget spacerHorizontalTiny = SizedBox(width: tiny);
  static const Widget spacerHorizontalSmall = SizedBox(width: small);
  static const Widget spacerHorizontalMedium = SizedBox(width: medium);
  static const Widget spacerHorizontalLarge = SizedBox(width: large);
  static const Widget spacerHorizontalExtraLarge = SizedBox(width: extraLarge);
  
  /// Animation curves
  static const Curve curveDefault = Curves.easeInOut;
  static const Curve curveEaseIn = Curves.easeIn;
  static const Curve curveEaseOut = Curves.easeOut;
  static const Curve curveBounce = Curves.bounceOut;
  static const Curve curveElastic = Curves.elasticOut;
  
  /// Get duration
  static Duration duration(int milliseconds) => Duration(milliseconds: milliseconds);
  static Duration get durationFastMs => Duration(milliseconds: durationFast);
  static Duration get durationNormalMs => Duration(milliseconds: durationNormal);
  static Duration get durationSlowMs => Duration(milliseconds: durationSlow);
  static Duration get durationExtraSlowMs => Duration(milliseconds: durationExtraSlow);
}
