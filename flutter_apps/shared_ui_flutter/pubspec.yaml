name: shared_ui_flutter
description: Shared UI Components Library for Flutter Apps
version: 1.0.0
publish_to: 'none'

environment:
  sdk: '>=3.0.0 <4.0.0'
  flutter: '>=3.0.0'

dependencies:
  flutter:
    sdk: flutter
  
  # Core UI Dependencies
  google_fonts: ^6.1.0
  flutter_animate: ^4.5.0
  
  # Icons
  lucide_icons_flutter: ^1.3.0
  heroicons_flutter: ^0.3.0
  
  # State Management
  provider: ^6.1.2
  
  # Utilities
  collection: ^1.18.0
  intl: ^0.20.2
  
  # UI Components
  shimmer: ^3.0.0
  badges: ^3.1.2
  
  # Animations
  lottie: ^3.1.2

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^4.0.0

flutter:
  uses-material-design: true
  
  assets:
    - assets/animations/
    - assets/icons/