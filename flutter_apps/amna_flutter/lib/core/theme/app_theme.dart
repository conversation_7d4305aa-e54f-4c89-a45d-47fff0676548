import 'package:flutter/material.dart';
import 'package:shared_ui_flutter/shared_ui_flutter.dart';

/// AMNA App Theme using Luminar Design System
///
/// This theme configuration uses the unified design system from shared_ui_flutter
/// to ensure consistency with the migration apps' orange/amber color scheme.
class AmnaAppTheme {
  // Private constructor to prevent instantiation
  AmnaAppTheme._();

  /// Light theme using Luminar Design System
  static ThemeData get lightTheme => AppTheme.lightTheme.copyWith(
    extensions: [
      ShadowTheme.light,
      AnimationTheme.standard,
      SpacingTheme.mobile,
    ],
  );

  /// Dark theme using Luminar Design System
  static ThemeData get darkTheme => AppTheme.darkTheme.copyWith(
    extensions: [
      ShadowTheme.dark,
      AnimationTheme.standard,
      SpacingTheme.mobile,
    ],
  );

  /// Get responsive theme based on screen size
  static ThemeData getResponsiveTheme(BuildContext context, {bool isDark = false}) {
    final width = MediaQuery.of(context).size.width;

    SpacingTheme spacingTheme;
    if (width < AppSpacing.breakpointTablet) {
      spacingTheme = SpacingTheme.mobile;
    } else if (width < AppSpacing.breakpointDesktop) {
      spacingTheme = SpacingTheme.tablet;
    } else {
      spacingTheme = SpacingTheme.desktop;
    }

    final baseTheme = isDark ? AppTheme.darkTheme : AppTheme.lightTheme;

    return baseTheme.copyWith(
      extensions: [
        isDark ? ShadowTheme.dark : ShadowTheme.light,
        AnimationTheme.standard,
        spacingTheme,
      ],
    );
  }
}
