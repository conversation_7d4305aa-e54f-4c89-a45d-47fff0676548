import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:provider/provider.dart';
import 'package:sentry_flutter/sentry_flutter.dart';
import 'package:flutter_localizations/flutter_localizations.dart';

import 'core/config/app_config.dart';
import 'core/theme/app_theme.dart';
import 'core/router/app_router.dart';
import 'core/services/service_locator.dart';
import 'features/auth/presentation/bloc/auth_bloc.dart';
import 'features/chat/presentation/bloc/chat_bloc.dart';
import 'features/integration/presentation/provider/integration_provider.dart';
import 'features/personalization/presentation/provider/personalization_provider.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  
  // Initialize services
  await setupServiceLocator();
  
  // Initialize Sentry for error tracking
  await SentryFlutter.init(
    (options) {
      options.dsn = AppConfig.sentryDsn;
      options.tracesSampleRate = 1.0;
    },
    appRunner: () => runApp(const AmnaApp()),
  );
}

class AmnaApp extends StatelessWidget {
  const AmnaApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MultiBlocProvider(
      providers: [
        BlocProvider<AuthBloc>(
          create: (context) => getIt<AuthBloc>()..add(CheckAuthStatus()),
        ),
        BlocProvider<ChatBloc>(
          create: (context) => getIt<ChatBloc>(),
        ),
      ],
      child: MultiProvider(
        providers: [
          ChangeNotifierProvider(
            create: (_) => IntegrationProvider(),
          ),
          ChangeNotifierProvider(
            create: (_) => PersonalizationProvider(),
          ),
        ],
        child: Consumer<PersonalizationProvider>(
          builder: (context, personalization, child) {
            return MaterialApp.router(
              title: 'AMNA Chat',
              debugShowCheckedModeBanner: false,
              theme: AmnaAppTheme.lightTheme,
              darkTheme: AmnaAppTheme.darkTheme,
              themeMode: personalization.themeMode,
              localizationsDelegates: const [
                GlobalMaterialLocalizations.delegate,
                GlobalWidgetsLocalizations.delegate,
                GlobalCupertinoLocalizations.delegate,
              ],
              supportedLocales: const [
                Locale('en', 'US'),
                Locale('es', 'ES'),
                Locale('fr', 'FR'),
              ],
              routerConfig: AppRouter.router,
            );
          },
        ),
      ),
    );
  }
}