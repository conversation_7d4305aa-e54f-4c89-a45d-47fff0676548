name: amna_flutter
description: AMNA Chat Application - Flutter Version
version: 1.0.0+1
publish_to: 'none'

environment:
  sdk: '>=3.0.0 <4.0.0'

dependencies:
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter
  
  # UI & Theming
  cupertino_icons: ^1.0.6
  google_fonts: ^6.1.0
  flutter_animate: ^4.5.0
  shared_ui_flutter:
    path: ../shared_ui_flutter
  
  # State Management
  flutter_bloc: ^8.1.6
  provider: ^6.1.2
  
  # Navigation
  go_router: ^14.2.3
  
  # Networking
  dio: ^5.5.0+1
  web_socket_channel: ^3.0.1
  socket_io_client: ^2.0.3+1
  
  # Local Storage
  shared_preferences: ^2.2.3
  hive: ^2.2.3
  hive_flutter: ^1.1.0
  
  # Utilities
  uuid: ^4.4.2
  intl: ^0.20.2
  url_launcher: ^6.3.0
  path_provider: ^2.1.4
  
  # Authentication
  flutter_secure_storage: ^9.2.2
  
  # File handling
  file_picker: ^8.0.7
  image_picker: ^1.1.2
  
  # UI Components
  shimmer: ^3.0.0
  lottie: ^3.1.2
  flutter_slidable: ^3.1.1
  
  # Icons
  lucide_icons_flutter: ^1.3.0
  
  # Analytics & Monitoring
  sentry_flutter: ^8.5.0

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^4.0.0
  build_runner: ^2.4.11
  hive_generator: ^2.0.1

flutter:
  uses-material-design: true
  
  assets:
    - assets/images/
    - assets/animations/
    - assets/icons/
  
  fonts:
    - family: Inter
      fonts:
        - asset: assets/fonts/Inter-Regular.ttf
        - asset: assets/fonts/Inter-Medium.ttf
          weight: 500
        - asset: assets/fonts/Inter-SemiBold.ttf
          weight: 600
        - asset: assets/fonts/Inter-Bold.ttf
          weight: 700